# In this Docker Compose example, we only fire up the services required for local development.
# This is not advised for production use as it exposes ports to the database insecurely.
# If you're looking for a production-ready Docker Compose file, check out the `traefik.yml` file.

services:
  # Database (Postgres)
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    ports:
      - ${POSTGRES_PORT:-5432}:5432
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-postgres}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Adminer (for database management)
  adminer:
    image: shyim/adminerevo:latest
    restart: unless-stopped
    ports:
      - 5555:8080
    environment:
      ADMINER_DEFAULT_DRIVER: pgsql
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DEFAULT_USER: ${POSTGRES_USER:-postgres}
      ADMINER_DEFAULT_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      ADMINER_DEFAULT_DB: ${POSTGRES_DB:-postgres}

  # Storage (for image uploads)
  minio:
    image: minio/minio:latest
    restart: unless-stopped
    command: server /data
    ports:
      - ${STORAGE_PORT:-9000}:9000
      - "9001:9001" # Minio Console (Optional)
    volumes:
      - minio_data:/data
    environment:
      MINIO_ADDRESS: :9000
      MINIO_CONSOLE_ADDRESS: :9001
      MINIO_ROOT_USER: ${STORAGE_ACCESS_KEY:-minioadmin}
      MINIO_ROOT_PASSWORD: ${STORAGE_SECRET_KEY:-minioadmin}

  # Chrome Browser (for printing and previews)
  chrome:
    image: ghcr.io/browserless/chromium:latest
    restart: unless-stopped
    ports:
      - ${CHROME_PORT:-8080}:3000
    environment:
      HEALTH: "true"
      TOKEN: ${CHROME_TOKEN:-chrome_token}
      PROXY_HOST: "localhost"
      PROXY_PORT: ${CHROME_PORT:-8080}
      PROXY_SSL: "false"

volumes:
  minio_data:
  postgres_data:
